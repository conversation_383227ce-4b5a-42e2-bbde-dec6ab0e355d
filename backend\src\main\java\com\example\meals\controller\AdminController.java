package com.example.meals.controller;

import com.example.meals.dto.AdminLoginRequest;
import com.example.meals.dto.AdminRegisterRequest;
import com.example.meals.dto.AdminEmailCodeLoginRequest;
import com.example.meals.dto.AdminResponse;
import com.example.meals.dto.PageResponse;
import com.example.meals.dto.UserResponse;
import com.example.meals.dto.UserStatsResponse;
import com.example.meals.service.AdminService;
import com.example.meals.service.EmailVerificationService;
import com.example.meals.common.Result;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;
import com.example.meals.utils.AuthUtil;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
public class AdminController {

    @Autowired
    private AdminService adminService;

    @Autowired
    private EmailVerificationService emailVerificationService;
    
    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<AdminResponse> login(@RequestBody AdminLoginRequest request) {
        return adminService.login(request);
    }

    /**
     * 管理员注册
     */
    @PostMapping("/register")
    public Result<AdminResponse> register(@RequestBody AdminRegisterRequest request) {
        return adminService.register(request);
    }

    /**
     * 管理员邮箱验证码登录
     */
    @PostMapping("/login-with-code")
    public Result<AdminResponse> loginWithCode(@RequestBody AdminEmailCodeLoginRequest request) {
        return adminService.loginWithEmailCode(request);
    }

    /**
     * 发送管理员注册邮箱验证码
     */
    @PostMapping("/send-register-code")
    public Result<Void> sendRegisterCode(@RequestParam String email) {
        return emailVerificationService.sendVerificationCode(email, "ADMIN_REGISTER");
    }

    /**
     * 发送管理员登录邮箱验证码
     */
    @PostMapping("/send-login-code")
    public Result<Void> sendLoginCode(@RequestParam String email) {
        return emailVerificationService.sendVerificationCode(email, "ADMIN_LOGIN");
    }


    
    /**
     * 删除管理员
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteAdmin(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return authResult;
        }
        return adminService.deleteAdmin(id);
    }
    
    /**
     * 根据ID获取管理员信息
     */
    @GetMapping("/{id}")
    public Result<AdminResponse> getAdminById(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        String userType = (String) request.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return adminService.getAdminById(id);
    }
    
    /**
     * 分页查询管理员列表
     */
    @GetMapping("/list")
    public Result<PageResponse<AdminResponse>> getAdminList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String realName,
            @RequestParam(required = false) Integer status,
            HttpServletRequest request) {
        // 验证管理员权限
        String userType = (String) request.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return adminService.getAdminList(page, size, username, realName, status);
    }
    
    /**
     * 启用/禁用管理员
     */
    @PutMapping("/{id}/status")
    public Result<Void> toggleAdminStatus(@PathVariable Long id, @RequestParam Integer status, HttpServletRequest request) {
        // 验证管理员权限
        String userType = (String) request.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return adminService.toggleAdminStatus(id, status);
    }
    
    /**
     * 重置管理员密码
     */
    @PutMapping("/{id}/reset-password")
    public Result<Void> resetPassword(@PathVariable Long id, @RequestParam String newPassword) {
        return adminService.resetPassword(id, newPassword);
    }
    
    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        return adminService.checkUsername(username);
    }
    
    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        return adminService.checkEmail(email);
    }

    // ==================== 用户管理接口 ====================

    /**
     * 分页查询用户列表（管理员功能）
     */
    @GetMapping("/users")
    public Result<PageResponse<UserResponse>> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) Integer status,
            HttpServletRequest request) {
        // 验证管理员权限
        String userType = (String) request.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return adminService.getUserList(page, size, username, email, status);
    }

    /**
     * 启用/禁用用户
     */
    @PutMapping("/users/{id}/status")
    public Result<Void> toggleUserStatus(@PathVariable Long id, @RequestParam Integer status, HttpServletRequest request) {
        // 验证管理员权限
        String userType = (String) request.getAttribute("userType");
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        return adminService.toggleUserStatus(id, status);
    }

    /**
     * 删除用户（管理员功能）
     */
    @DeleteMapping("/users/{id}")
    public Result<Void> deleteUser(@PathVariable Long id) {
        return adminService.deleteUser(id);
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/users/stats")
    public Result<UserStatsResponse> getUserStats() {
        return adminService.getUserStats();
    }
}
