package com.example.meals.service.impl;

import com.example.meals.dto.*;
import com.example.meals.entity.Admin;
import com.example.meals.entity.User;
import com.example.meals.mapper.AdminMapper;
import com.example.meals.mapper.UserMapper;
import com.example.meals.service.AdminService;
import com.example.meals.service.EmailVerificationService;
import com.example.meals.common.Result;
import com.example.meals.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 管理员服务实现类
 */
@Service
public class AdminServiceImpl implements AdminService {
    
    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private EmailVerificationService emailVerificationService;
    
    // 邮箱正则表达式
    private static final String EMAIL_PATTERN = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
    
    @Override
    public Result<AdminResponse> login(AdminLoginRequest request) {
        // 参数验证
        if (!StringUtils.hasText(request.getUsername())) {
            return Result.badRequest("用户名不能为空");
        }
        if (!StringUtils.hasText(request.getPassword())) {
            return Result.badRequest("密码不能为空");
        }


        
        try {
            // 根据用户名查询管理员
            Admin admin = adminMapper.selectByUsername(request.getUsername());
            if (admin == null) {
    
                return Result.badRequest("用户名或密码错误");
            }

            // 检查管理员状态
            if (admin.getStatus() == 0) {
                return Result.forbidden("账户已被禁用");
            }

            // 验证密码
            if (!passwordEncoder.matches(request.getPassword(), admin.getPassword())) {
                return Result.badRequest("用户名或密码错误");
            }
            
            // 更新最后登录时间
            adminMapper.updateLastLoginTime(admin.getId(), LocalDateTime.now());
            admin.setLastLoginTime(LocalDateTime.now());

            // 生成JWT Token
            String token = jwtUtil.generateToken(admin.getId(), admin.getUsername(), "ADMIN");

            // 创建响应对象，包含token
            AdminResponse adminResponse = new AdminResponse(admin);
            adminResponse.setToken(token);

            return Result.success("登录成功", adminResponse);
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    @Override
    public Result<AdminResponse> register(AdminRegisterRequest request) {
        try {
            // 参数验证
            if (!StringUtils.hasText(request.getUsername())) {
                return Result.badRequest("用户名不能为空");
            }
            if (!StringUtils.hasText(request.getEmail())) {
                return Result.badRequest("邮箱不能为空");
            }
            if (!StringUtils.hasText(request.getRealName())) {
                return Result.badRequest("真实姓名不能为空");
            }
            if (!StringUtils.hasText(request.getPassword())) {
                return Result.badRequest("密码不能为空");
            }
            if (!StringUtils.hasText(request.getVerificationCode())) {
                return Result.badRequest("邮箱验证码不能为空");
            }

            // 验证用户名格式
            if (request.getUsername().length() < 3 || request.getUsername().length() > 20) {
                return Result.badRequest("用户名长度必须在3-20个字符之间");
            }
            if (!request.getUsername().matches("^[a-zA-Z0-9_]+$")) {
                return Result.badRequest("用户名只能包含字母、数字和下划线");
            }

            // 验证邮箱格式
            if (!request.getEmail().matches(EMAIL_PATTERN)) {
                return Result.badRequest("邮箱格式不正确");
            }

            // 验证邮箱验证码
            Result<Void> verifyResult = emailVerificationService.verifyCode(
                request.getEmail(), request.getVerificationCode(), "ADMIN_REGISTER");
            if (!verifyResult.getSuccess()) {
                return Result.badRequest("邮箱验证码" + verifyResult.getMessage().replace("验证码", ""));
            }

            // 验证真实姓名
            if (request.getRealName().length() < 2 || request.getRealName().length() > 10) {
                return Result.badRequest("真实姓名长度必须在2-10个字符之间");
            }

            // 验证密码
            if (request.getPassword().length() < 6 || request.getPassword().length() > 20) {
                return Result.badRequest("密码长度必须在6-20个字符之间");
            }

            // 检查用户名是否已存在
            if (adminMapper.countByUsername(request.getUsername()) > 0) {
                return Result.badRequest("该用户名已被使用");
            }

            // 检查邮箱是否已存在
            if (adminMapper.countByEmail(request.getEmail()) > 0) {
                return Result.badRequest("该邮箱已被使用");
            }

            // 创建管理员对象
            Admin admin = new Admin();
            admin.setUsername(request.getUsername());
            admin.setEmail(request.getEmail());
            admin.setRealName(request.getRealName());
            admin.setPassword(passwordEncoder.encode(request.getPassword()));
            admin.setStatus(1); // 默认启用
            admin.setCreateTime(LocalDateTime.now());
            admin.setUpdateTime(LocalDateTime.now());

            // 保存到数据库
            int result = adminMapper.insert(admin);
            if (result > 0) {
                // 注册成功，返回管理员信息（不包含密码）
                AdminResponse adminResponse = new AdminResponse(admin);
                return Result.success("管理员注册成功", adminResponse);
            } else {
                return Result.error("注册失败，请稍后重试");
            }

        } catch (Exception e) {
            return Result.error("注册失败：" + e.getMessage());
        }
    }

    @Override
    public Result<AdminResponse> loginWithEmailCode(AdminEmailCodeLoginRequest request) {
        // 参数验证
        if (!StringUtils.hasText(request.getEmail())) {
            return Result.badRequest("邮箱不能为空");
        }
        if (!StringUtils.hasText(request.getVerificationCode())) {
            return Result.badRequest("验证码不能为空");
        }

        // 验证邮箱格式
        if (!request.getEmail().matches(EMAIL_PATTERN)) {
            return Result.badRequest("邮箱格式不正确");
        }

        try {
            // 验证验证码（ADMIN_LOGIN类型）
            Result<Void> verifyResult = emailVerificationService.verifyCode(
                request.getEmail(),
                request.getVerificationCode(),
                "ADMIN_LOGIN"
            );

            if (!verifyResult.getSuccess()) {
                return Result.badRequest(verifyResult.getMessage());
            }

            // 根据邮箱查询管理员
            Admin admin = adminMapper.selectByEmail(request.getEmail());
            if (admin == null) {
                return Result.badRequest("管理员不存在");
            }

            // 检查管理员状态
            if (admin.getStatus() == 0) {
                return Result.forbidden("账户已被禁用");
            }

            // 更新最后登录时间
            admin.setLastLoginTime(LocalDateTime.now());
            adminMapper.updateLastLoginTime(admin.getId(), admin.getLastLoginTime());

            // 生成JWT Token
            String token = jwtUtil.generateToken(admin.getId(), admin.getUsername(), "ADMIN");

            // 创建响应对象，包含token
            AdminResponse adminResponse = new AdminResponse(admin);
            adminResponse.setToken(token);

            return Result.success("登录成功", adminResponse);
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }



    
    @Override
    public Result<Void> deleteAdmin(Long id) {
        if (id == null || id <= 0) {
            return Result.badRequest("管理员ID不能为空");
        }
        
        // 查询管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            return Result.notFound("管理员不存在");
        }
        
        try {
            int result = adminMapper.deleteById(id);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("删除失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("删除失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<AdminResponse> getAdminById(Long id) {
        if (id == null || id <= 0) {
            return Result.badRequest("管理员ID不能为空");
        }
        
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            return Result.notFound("管理员不存在");
        }
        
        return Result.success(new AdminResponse(admin));
    }
    
    @Override
    public Result<PageResponse<AdminResponse>> getAdminList(Integer page, Integer size,
                                                            String username, String realName,
                                                            Integer status) {
        // 参数验证和默认值设置
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1 || size > 100) {
            size = 10;
        }
        
        try {
            // 计算偏移量
            int offset = (page - 1) * size;
            
            // 查询管理员列表
            List<Admin> adminList = adminMapper.selectByCondition(username, realName, null, status, offset, size);
            List<AdminResponse> responseList = adminList.stream()
                    .map(AdminResponse::new)
                    .collect(Collectors.toList());

            // 查询总数
            int total = adminMapper.countByCondition(username, realName, null, status);
            
            // 构建分页响应
            PageResponse<AdminResponse> pageResponse = PageResponse.of(responseList, (long) total, page, size);
            
            return Result.success(pageResponse);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> toggleAdminStatus(Long id, Integer status) {
        if (id == null || id <= 0) {
            return Result.badRequest("管理员ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            return Result.badRequest("状态参数错误");
        }
        
        // 查询管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            return Result.notFound("管理员不存在");
        }
        
        try {
            Admin updateAdmin = new Admin();
            updateAdmin.setId(id);
            updateAdmin.setStatus(status);
            updateAdmin.setUpdateTime(LocalDateTime.now());
            
            int result = adminMapper.updateById(updateAdmin);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("操作失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("操作失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> resetPassword(Long id, String newPassword) {
        if (id == null || id <= 0) {
            return Result.badRequest("管理员ID不能为空");
        }
        if (!StringUtils.hasText(newPassword)) {
            return Result.badRequest("新密码不能为空");
        }
        if (newPassword.length() < 6) {
            return Result.badRequest("密码长度不能少于6位");
        }
        
        // 查询管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            return Result.notFound("管理员不存在");
        }
        
        try {
            Admin updateAdmin = new Admin();
            updateAdmin.setId(id);
            updateAdmin.setPassword(passwordEncoder.encode(newPassword)); // 使用BCrypt加密密码
            updateAdmin.setUpdateTime(LocalDateTime.now());
            
            int result = adminMapper.updateById(updateAdmin);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("密码重置失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("密码重置失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Boolean> checkUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return Result.badRequest("用户名不能为空");
        }
        
        boolean available = adminMapper.countByUsername(username) == 0;
        return Result.success(available);
    }
    
    @Override
    public Result<Boolean> checkEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return Result.badRequest("邮箱不能为空");
        }
        
        if (!Pattern.matches(EMAIL_PATTERN, email)) {
            return Result.badRequest("邮箱格式不正确");
        }
        
        boolean available = adminMapper.countByEmail(email) == 0;
        return Result.success(available);
    }

    // ==================== 用户管理功能实现 ====================

    @Override
    public Result<PageResponse<UserResponse>> getUserList(Integer page, Integer size,
                                                          String username, String email,
                                                          Integer status) {
        // 参数验证和默认值设置
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1 || size > 100) {
            size = 10;
        }

        try {
            // 计算偏移量
            int offset = (page - 1) * size;

            // 查询用户列表
            List<User> userList = userMapper.selectByCondition(username, email, status, offset, size);
            List<UserResponse> responseList = userList.stream()
                    .map(UserResponse::new)
                    .collect(Collectors.toList());

            // 查询总数
            int total = userMapper.countByCondition(username, email, status);

            // 构建分页响应
            PageResponse<UserResponse> pageResponse = PageResponse.of(responseList, (long) total, page, size);

            return Result.success(pageResponse);
        } catch (Exception e) {
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Void> toggleUserStatus(Long id, Integer status) {
        if (id == null || id <= 0) {
            return Result.badRequest("用户ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            return Result.badRequest("状态参数错误");
        }

        // 查询用户是否存在
        User user = userMapper.selectById(id);
        if (user == null) {
            return Result.notFound("用户不存在");
        }

        try {
            User updateUser = new User();
            updateUser.setId(id);
            updateUser.setStatus(status);
            updateUser.setUpdateTime(LocalDateTime.now());

            int result = userMapper.updateById(updateUser);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("操作失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteUser(Long id) {
        if (id == null || id <= 0) {
            return Result.badRequest("用户ID不能为空");
        }

        // 查询用户是否存在
        User user = userMapper.selectById(id);
        if (user == null) {
            return Result.notFound("用户不存在");
        }

        try {
            int result = userMapper.deleteById(id);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("删除失败，请重试");
            }
        } catch (Exception e) {
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    @Override
    public Result<UserStatsResponse> getUserStats() {
        try {
            // 获取用户统计信息
            int totalUsers = userMapper.countAll();
            int activeUsers = userMapper.countByCondition(null, null, 1);
            int inactiveUsers = userMapper.countByCondition(null, null, 0);
            int todayRegistered = userMapper.countTodayActive();

            UserStatsResponse stats = new UserStatsResponse(
                    (long) totalUsers,
                    (long) activeUsers,
                    (long) inactiveUsers,
                    (long) todayRegistered
            );

            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取统计信息失败：" + e.getMessage());
        }
    }

}
